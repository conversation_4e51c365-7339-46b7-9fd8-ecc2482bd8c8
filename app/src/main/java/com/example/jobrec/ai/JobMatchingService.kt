package com.example.jobrec.ai

import android.util.Log
import com.example.jobrec.services.GeminiJobMatchingService
import com.example.jobrec.Job
import com.example.jobrec.models.JobMatch
import com.example.jobrec.models.MatchCriteria
import com.example.jobrec.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class JobMatchingService {
    private val geminiService = GeminiJobMatchingService()
    private val TAG = "JobMatchingService"

    suspend fun calculateJobMatch(user: User, job: Job): JobMatch {
        return withContext(Dispatchers.IO) {
            try {
                val matchCriteria = extractMatchCriteria(user)
                val aiMatchResult = getAIMatchAnalysis(matchCriteria, job)
                val ruleBasedMatch = calculateRuleBasedMatch(user, job)

                val finalPercentage = (aiMatchResult.first + ruleBasedMatch) / 2
                val reasoning = aiMatchResult.second

                JobMatch(
                    job = job.copy(
                        matchPercentage = finalPercentage,
                        matchReasoning = reasoning
                    ),
                    matchPercentage = finalPercentage,
                    matchReasoning = reasoning,
                    skillsMatch = calculateSkillsMatch(user.skills, job.requirements),
                    experienceMatch = calculateExperienceMatch(user.experience, job.experienceLevel),
                    educationMatch = calculateEducationMatch(user.education, job.jobField),
                    locationMatch = calculateLocationMatch(user.province, job.province)
                )
            } catch (e: Exception) {
                Log.e(TAG, "Error calculating job match", e)
                val fallbackMatch = calculateRuleBasedMatch(user, job)
                JobMatch(
                    job = job.copy(
                        matchPercentage = fallbackMatch,
                        matchReasoning = "Match calculated using profile analysis"
                    ),
                    matchPercentage = fallbackMatch,
                    matchReasoning = "Match calculated using profile analysis"
                )
            }
        }
    }

    private fun extractMatchCriteria(user: User): MatchCriteria {
        return MatchCriteria(
            skills = user.skills,
            experience = user.experience.map { "${it.position} at ${it.company} (${it.startDate} - ${it.endDate})" },
            education = user.education.map { "${it.degree} in ${it.fieldOfStudy} from ${it.institution}" },
            summary = user.summary,
            location = user.province,
            field = user.field,
            subField = user.subField,
            yearsOfExperience = user.yearsOfExperience
        )
    }

    private suspend fun getAIMatchAnalysis(criteria: MatchCriteria, job: Job): Pair<Int, String> {
        return try {
            val matchPercentage = geminiService.calculateJobMatch(
                userSkills = criteria.skills,
                userExperience = criteria.experience.joinToString(", "),
                userEducation = criteria.education.joinToString(", "),
                userField = extractUserField(criteria),
                userSubField = extractUserSubField(criteria),
                userYearsOfExperience = extractYearsOfExperience(criteria),
                jobTitle = job.title,
                jobDescription = job.description,
                jobRequirements = job.requirements,
                jobField = job.jobField,
                jobSpecialization = job.specialization,
                jobExperienceLevel = job.experienceLevel
            )

            val reasoning = generateMatchReasoning(matchPercentage, criteria, job)
            Pair(matchPercentage, reasoning)
        } catch (e: Exception) {
            Log.e(TAG, "Gemini AI analysis failed, using fallback", e)
            Pair(50, "Unable to perform detailed AI analysis")
        }
    }

    private fun extractUserField(criteria: MatchCriteria): String {
        return criteria.field
    }

    private fun extractUserSubField(criteria: MatchCriteria): String {
        return criteria.subField
    }

    private fun extractYearsOfExperience(criteria: MatchCriteria): String {
        return criteria.yearsOfExperience
    }

    private fun generateMatchReasoning(percentage: Int, criteria: MatchCriteria, job: Job): String {
        return when {
            percentage >= 80 -> "Excellent match! Your skills and experience align very well with this position."
            percentage >= 60 -> "Good match. You have relevant skills for this role with some areas for growth."
            percentage >= 40 -> "Moderate match. Some of your skills are relevant, but you may need additional experience."
            else -> "Limited match. This role may require skills or experience you don't currently have."
        }
    }



    private fun calculateRuleBasedMatch(user: User, job: Job): Int {
        // Enhanced rule-based matching with field/specialization as primary factor
        val fieldScore = calculateFieldSpecializationMatch(user.field, user.subField, job.jobField, job.specialization)
        val skillsScore = calculateSkillsMatch(user.skills, job.requirements)
        val experienceScore = calculateExperienceMatch(user.experience, job.experienceLevel)
        val educationScore = calculateEducationMatch(user.education, job.jobField)
        val locationScore = calculateLocationMatch(user.province, job.province)

        // Enhanced weighted calculation - field match is now most important
        val finalScore = ((fieldScore * 0.35) + (skillsScore * 0.30) + (experienceScore * 0.20) +
                         (educationScore * 0.10) + (locationScore * 0.05)).toInt()

        Log.d(TAG, "Rule-based match - Field: $fieldScore%, Skills: $skillsScore%, " +
                  "Experience: $experienceScore%, Education: $educationScore%, Location: $locationScore%, Final: $finalScore%")

        return finalScore.coerceAtMost(100)
    }

    private fun calculateFieldSpecializationMatch(userField: String, userSubField: String,
                                                jobField: String, jobSpecialization: String): Int {
        if (userField.isEmpty() || jobField.isEmpty()) return 50

        val userFieldLower = userField.lowercase().trim()
        val userSubFieldLower = userSubField.lowercase().trim()
        val jobFieldLower = jobField.lowercase().trim()
        val jobSpecializationLower = jobSpecialization.lowercase().trim()

        return when {
            // Perfect match: same field and specialization
            userFieldLower == jobFieldLower && userSubFieldLower == jobSpecializationLower -> 95
            // Good match: same field, related specialization
            userFieldLower == jobFieldLower && (userSubFieldLower.contains(jobSpecializationLower) ||
                jobSpecializationLower.contains(userSubFieldLower)) -> 85
            // Fair match: same field, different specialization
            userFieldLower == jobFieldLower -> 75
            // Related fields (e.g., IT and Computer Science)
            areFieldsRelated(userFieldLower, jobFieldLower) -> 65
            // Transferable skills between fields
            areFieldsTransferable(userFieldLower, jobFieldLower) -> 45
            else -> 25
        }
    }

    private fun areFieldsRelated(field1: String, field2: String): Boolean {
        val relatedGroups = listOf(
            listOf("information technology", "computer science", "software engineering", "software development"),
            listOf("engineering", "mechanical engineering", "electrical engineering", "civil engineering", "industrial engineering"),
            listOf("finance", "accounting", "economics", "business administration"),
            listOf("marketing", "sales", "business development", "communications", "advertising"),
            listOf("healthcare", "nursing", "medicine", "medical", "health sciences"),
            listOf("education", "teaching", "training", "academic", "curriculum development")
        )

        return relatedGroups.any { group ->
            group.any { field1.contains(it) } && group.any { field2.contains(it) }
        }
    }

    private fun areFieldsTransferable(field1: String, field2: String): Boolean {
        val transferableFields = listOf(
            "management", "project management", "business", "administration",
            "customer service", "sales", "communications", "research", "consulting"
        )

        return transferableFields.any { transferable ->
            (field1.contains(transferable) || field2.contains(transferable))
        }
    }

    private fun calculateSkillsMatch(userSkills: List<String>, jobRequirements: String): Int {
        if (userSkills.isEmpty()) return 20
        if (jobRequirements.isEmpty()) return 40

        val requirements = jobRequirements.lowercase()
        var exactMatchCount = 0
        var partialMatchCount = 0
        var totalSkills = userSkills.size

        // Enhanced skill matching with synonyms and related skills
        for (skill in userSkills) {
            val skillLower = skill.lowercase().trim()
            if (skillLower.isEmpty()) continue

            when {
                // Direct exact match
                requirements.contains(skillLower) -> exactMatchCount++
                // Check for related/synonym skills
                hasRelatedSkillInRequirements(skillLower, requirements) -> partialMatchCount++
            }
        }

        // Calculate weighted match percentage
        val exactMatchPercentage = if (totalSkills > 0) {
            (exactMatchCount.toDouble() / totalSkills * 100).toInt()
        } else 0

        val partialMatchPercentage = if (totalSkills > 0) {
            (partialMatchCount.toDouble() / totalSkills * 50).toInt() // Partial matches worth 50%
        } else 0

        val combinedMatchPercentage = exactMatchPercentage + partialMatchPercentage

        // Enhanced scoring based on combined match percentage
        return when {
            combinedMatchPercentage >= 80 -> 90
            combinedMatchPercentage >= 60 -> 80
            combinedMatchPercentage >= 40 -> 70
            combinedMatchPercentage >= 20 -> 60
            combinedMatchPercentage > 0 -> 50
            else -> 30
        }.coerceAtMost(100)
    }

    private fun hasRelatedSkillInRequirements(userSkill: String, requirements: String): Boolean {
        // Enhanced skill synonyms and related terms
        val skillSynonyms = mapOf(
            "javascript" to listOf("js", "node.js", "react", "angular", "vue.js", "typescript", "frontend"),
            "python" to listOf("django", "flask", "pandas", "numpy", "machine learning", "data science"),
            "java" to listOf("spring", "hibernate", "maven", "gradle", "backend", "enterprise"),
            "sql" to listOf("mysql", "postgresql", "database", "oracle", "data analysis", "queries"),
            "html" to listOf("web development", "frontend", "markup", "css"),
            "css" to listOf("styling", "frontend", "web design", "bootstrap", "sass"),
            "project management" to listOf("scrum", "agile", "kanban", "pmp", "planning", "coordination"),
            "communication" to listOf("presentation", "public speaking", "writing", "interpersonal"),
            "leadership" to listOf("management", "team lead", "supervision", "mentoring"),
            "microsoft office" to listOf("excel", "word", "powerpoint", "outlook", "office suite"),
            "data analysis" to listOf("analytics", "reporting", "excel", "tableau", "power bi"),
            "customer service" to listOf("support", "client relations", "help desk", "customer care"),
            "sales" to listOf("business development", "account management", "revenue", "client acquisition")
        )

        return skillSynonyms[userSkill]?.any { synonym ->
            requirements.contains(synonym)
        } ?: false
    }

    private fun extractJobTitle(requirements: String): String {
        // Try to extract job context from requirements
        val commonTitles = listOf("developer", "engineer", "designer", "manager", "analyst", "tester", "consultant")
        return commonTitles.find { requirements.contains(it) } ?: ""
    }

    private fun isCompleteMismatch(userSkills: List<String>, jobRequirements: String): Boolean {
        val userIsTechnical = userSkills.any { isTechnicalSkill(it.lowercase()) }
        val jobIsTechnical = isTechnicalJob(jobRequirements)

        // If user is clearly technical but job is clearly non-technical (or vice versa)
        return (userIsTechnical && !jobIsTechnical && !jobRequirements.contains("technical")) ||
               (!userIsTechnical && jobIsTechnical)
    }

    private fun isTechnicalSkill(skill: String): Boolean {
        val technicalSkills = listOf("programming", "coding", "development", "software", "web", "mobile", "database", "api", "framework", "library")
        return technicalSkills.any { skill.contains(it) }
    }

    private fun isTechnicalJob(requirements: String): Boolean {
        val technicalKeywords = listOf("programming", "coding", "development", "software", "web", "mobile", "database", "api", "technical")
        return technicalKeywords.any { requirements.contains(it) }
    }

    private fun isRelatedSkill(skill: String, requirements: String): Boolean {
        val relatedSkills = mapOf(
            "java" to listOf("spring", "android", "kotlin", "programming"),
            "kotlin" to listOf("java", "android", "programming"),
            "python" to listOf("django", "flask", "data", "programming"),
            "javascript" to listOf("react", "node", "web", "frontend", "backend"),
            "react" to listOf("javascript", "frontend", "web"),
            "sql" to listOf("database", "mysql", "postgresql", "data"),
            "html" to listOf("css", "web", "frontend"),
            "css" to listOf("html", "web", "frontend", "design")
        )

        return relatedSkills[skill]?.any { related ->
            requirements.contains(related)
        } ?: false
    }

    private fun calculateExperienceMatch(userExperience: List<com.example.jobrec.Experience>, jobExperienceLevel: String): Int {
        val totalYears = userExperience.sumOf { parseExperienceYears(it.startDate, it.endDate) }
        val experienceLevelLower = jobExperienceLevel.lowercase()

        return when {
            // Entry level
            experienceLevelLower.contains("entry") || experienceLevelLower.contains("junior") -> {
                if (totalYears <= 2) 90 else 75
            }
            // Mid level
            experienceLevelLower.contains("mid") || experienceLevelLower.contains("intermediate") -> {
                if (totalYears in 2..5) 90 else if (totalYears < 2) 60 else 80
            }
            // Senior level
            experienceLevelLower.contains("senior") || experienceLevelLower.contains("lead") -> {
                if (totalYears >= 5) 90 else if (totalYears >= 2) 70 else 50
            }
            // No specific level
            else -> 75
        }
    }

    private fun parseExperienceYears(startDate: String, endDate: String): Int {
        return try {
            val startYear = startDate.substringAfterLast("/").toIntOrNull() ?:
                           startDate.substringAfterLast("-").toIntOrNull() ?:
                           java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)

            val endYear = if (endDate.lowercase().contains("present") || endDate.lowercase().contains("current") || endDate.isEmpty()) {
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            } else {
                endDate.substringAfterLast("/").toIntOrNull() ?:
                endDate.substringAfterLast("-").toIntOrNull() ?:
                java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
            }

            kotlin.math.max(1, endYear - startYear)
        } catch (e: Exception) {
            1
        }
    }

    private fun calculateEducationMatch(userEducation: List<com.example.jobrec.Education>, jobField: String): Int {
        if (userEducation.isEmpty()) return 50
        if (jobField.isEmpty()) return 70

        val jobFieldLower = jobField.lowercase()

        for (education in userEducation) {
            val fieldOfStudy = education.fieldOfStudy.lowercase()

            // Simple field matching
            if (fieldOfStudy.contains(jobFieldLower) || jobFieldLower.contains(fieldOfStudy)) {
                return 90 // Good match
            }
        }

        return 60 // Has education but not directly related
    }

    private fun isRelatedField(userField: String, jobField: String): Boolean {
        val relatedFields = mapOf(
            "computer science" to listOf("software", "programming", "technology", "it", "development"),
            "information technology" to listOf("software", "programming", "computer", "development"),
            "software engineering" to listOf("programming", "development", "technology", "computer"),
            "business" to listOf("management", "marketing", "finance", "administration"),
            "engineering" to listOf("technical", "development", "technology"),
            "design" to listOf("ui", "ux", "graphic", "creative", "visual")
        )

        return relatedFields.entries.any { (field, related) ->
            (userField.contains(field) && related.any { jobField.contains(it) }) ||
            (jobField.contains(field) && related.any { userField.contains(it) })
        }
    }

    private fun isTechnicalField(field: String): Boolean {
        val technicalKeywords = listOf("software", "programming", "development", "technology", "it", "computer", "engineering")
        return technicalKeywords.any { field.contains(it) }
    }

    private fun isTechnicalDegree(degree: String): Boolean {
        val technicalDegrees = listOf("computer", "software", "engineering", "technology", "information", "mathematics", "science")
        return technicalDegrees.any { degree.contains(it) }
    }

    private fun calculateLocationMatch(userLocation: String, jobLocation: String): Int {
        if (userLocation.isEmpty() || jobLocation.isEmpty()) return 80

        val userLoc = userLocation.lowercase().trim()
        val jobLoc = jobLocation.lowercase().trim()

        return when {
            userLoc == jobLoc -> 100
            jobLoc.contains("remote") -> 95
            userLoc.contains(jobLoc) || jobLoc.contains(userLoc) -> 85
            else -> 70
        }
    }
}
