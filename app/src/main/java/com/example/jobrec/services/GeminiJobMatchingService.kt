package com.example.jobrec.services

import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.util.Log
import com.example.jobrec.BuildConfig

class GeminiJobMatchingService {

    companion object {
        private const val TAG = "GeminiJobMatching"
        // Move API key to BuildConfig for security
        private val API_KEY = BuildConfig.GEMINI_API_KEY
    }

    private val generativeModel = GenerativeModel(
        modelName = "gemini-pro",
        apiKey = API_KEY,
        generationConfig = generationConfig {
            temperature = 0.2f
            topK = 3
            topP = 0.9f
            maxOutputTokens = 200
        }
    )

    suspend fun calculateJobMatch(
        userSkills: List<String>,
        userExperience: String,
        userEducation: String,
        userField: String,
        userSubField: String,
        userYearsOfExperience: String,
        jobTitle: String,
        jobDescription: String,
        jobRequirements: String,
        jobField: String,
        jobSpecialization: String,
        jobExperienceLevel: String
    ): Int = withContext(Dispatchers.IO) {
        try {
            val prompt = buildEnhancedPrompt(
                userSkills, userExperience, userEducation, userField, userSubField, userYearsOfExperience,
                jobTitle, jobDescription, jobRequirements, jobField, jobSpecialization, jobExperienceLevel
            )

            Log.d(TAG, "Sending enhanced prompt to Gemini")

            val response = generativeModel.generateContent(prompt)
            val responseText = response.text ?: "0"

            Log.d(TAG, "Gemini response: $responseText")

            // Extract percentage from response
            val percentage = extractPercentage(responseText)
            Log.d(TAG, "Extracted percentage: $percentage")

            // Validate the percentage is reasonable
            if (percentage in 0..100) {
                percentage
            } else {
                Log.w(TAG, "Invalid percentage from Gemini: $percentage, using fallback")
                calculateEnhancedFallbackMatch(userSkills, userField, userSubField, userYearsOfExperience,
                    jobDescription, jobRequirements, jobField, jobSpecialization, jobExperienceLevel)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error calculating job match with Gemini", e)
            // Enhanced fallback matching
            calculateEnhancedFallbackMatch(userSkills, userField, userSubField, userYearsOfExperience,
                jobDescription, jobRequirements, jobField, jobSpecialization, jobExperienceLevel)
        }
    }

    private fun buildEnhancedPrompt(
        userSkills: List<String>,
        userExperience: String,
        userEducation: String,
        userField: String,
        userSubField: String,
        userYearsOfExperience: String,
        jobTitle: String,
        jobDescription: String,
        jobRequirements: String,
        jobField: String,
        jobSpecialization: String,
        jobExperienceLevel: String
    ): String {
        return """
            You are an expert career counselor and job matching specialist. Analyze the compatibility between a candidate and a job posting.

            CANDIDATE PROFILE:
            • Field of Interest: $userField
            • Specialization: $userSubField
            • Years of Experience: $userYearsOfExperience
            • Skills: ${userSkills.joinToString(", ")}
            • Work Experience: $userExperience
            • Education: $userEducation

            JOB POSTING:
            • Job Title: $jobTitle
            • Job Field: $jobField
            • Specialization Required: $jobSpecialization
            • Experience Level Required: $jobExperienceLevel
            • Job Description: $jobDescription
            • Requirements: $jobRequirements

            EVALUATION CRITERIA (in order of importance):
            1. Field/Specialization Match (35%): How well does the candidate's field and specialization align with the job?
            2. Skills Compatibility (30%): Do the candidate's skills match the job requirements? Consider related/transferable skills.
            3. Experience Level Match (20%): Does the candidate's experience level match what's required?
            4. Education Relevance (10%): Is the candidate's education relevant to the position?
            5. Overall Fit (5%): General compatibility based on job description and candidate profile.

            SCORING GUIDELINES:
            • 90-100%: Excellent match - candidate is highly qualified
            • 75-89%: Good match - candidate meets most requirements
            • 60-74%: Fair match - candidate has potential with some gaps
            • 45-59%: Poor match - significant gaps in requirements
            • 0-44%: Very poor match - candidate not suitable

            Return ONLY a single number between 0-100 representing the overall match percentage. No explanation needed.
        """.trimIndent()
    }

    private fun extractPercentage(response: String): Int {
        return try {
            // Try to find a number in the response
            val numbers = Regex("\\d+").findAll(response)
            val percentage = numbers.firstOrNull()?.value?.toIntOrNull() ?: 0

            // Ensure it's within valid range
            when {
                percentage > 100 -> 100
                percentage < 0 -> 0
                else -> percentage
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting percentage from: $response", e)
            0
        }
    }

    private fun calculateEnhancedFallbackMatch(
        userSkills: List<String>,
        userField: String,
        userSubField: String,
        userYearsOfExperience: String,
        jobDescription: String,
        jobRequirements: String,
        jobField: String,
        jobSpecialization: String,
        jobExperienceLevel: String
    ): Int {
        return try {
            // 1. Field/Specialization Match (35% weight)
            val fieldMatch = calculateFieldMatch(userField, userSubField, jobField, jobSpecialization)

            // 2. Skills Match (30% weight)
            val skillsMatch = calculateSkillsMatch(userSkills, jobDescription, jobRequirements, userField)

            // 3. Experience Match (20% weight)
            val experienceMatch = calculateExperienceMatch(userYearsOfExperience, jobExperienceLevel)

            // 4. Education relevance (10% weight) - simplified for fallback
            val educationMatch = if (fieldMatch > 70) 80 else 60

            // 5. Overall fit (5% weight) - based on job description analysis
            val overallFit = calculateOverallFit(userField, userSubField, jobDescription)

            // Weighted calculation
            val finalScore = (fieldMatch * 0.35 + skillsMatch * 0.30 + experienceMatch * 0.20 +
                            educationMatch * 0.10 + overallFit * 0.05).toInt()

            Log.d(TAG, "Enhanced fallback match - Field: $fieldMatch%, Skills: $skillsMatch%, " +
                      "Experience: $experienceMatch%, Final: $finalScore%")

            finalScore.coerceIn(0, 100)

        } catch (e: Exception) {
            Log.e(TAG, "Error in enhanced fallback calculation", e)
            50 // Conservative fallback
        }
    }

    private fun calculateFieldMatch(userField: String, userSubField: String, jobField: String, jobSpecialization: String): Int {
        if (userField.isEmpty() || jobField.isEmpty()) return 50

        val userFieldLower = userField.lowercase().trim()
        val userSubFieldLower = userSubField.lowercase().trim()
        val jobFieldLower = jobField.lowercase().trim()
        val jobSpecializationLower = jobSpecialization.lowercase().trim()

        return when {
            // Exact field and specialization match
            userFieldLower == jobFieldLower && userSubFieldLower == jobSpecializationLower -> 95
            // Exact field match, related specialization
            userFieldLower == jobFieldLower && (userSubFieldLower.contains(jobSpecializationLower) ||
                jobSpecializationLower.contains(userSubFieldLower)) -> 85
            // Exact field match, different specialization
            userFieldLower == jobFieldLower -> 75
            // Related fields (e.g., "Software Engineering" and "Information Technology")
            areRelatedFields(userFieldLower, jobFieldLower) -> 65
            // Transferable fields
            areTransferableFields(userFieldLower, jobFieldLower) -> 45
            else -> 25
        }
    }

    private fun areRelatedFields(field1: String, field2: String): Boolean {
        val relatedFieldGroups = listOf(
            listOf("information technology", "computer science", "software engineering", "software development"),
            listOf("engineering", "mechanical engineering", "electrical engineering", "civil engineering"),
            listOf("finance", "accounting", "economics", "business"),
            listOf("marketing", "sales", "business development", "communications"),
            listOf("healthcare", "nursing", "medicine", "medical"),
            listOf("education", "teaching", "training", "academic")
        )

        return relatedFieldGroups.any { group ->
            group.any { field1.contains(it) } && group.any { field2.contains(it) }
        }
    }

    private fun areTransferableFields(field1: String, field2: String): Boolean {
        val transferableSkillsFields = listOf(
            "management", "project management", "business", "administration",
            "customer service", "sales", "communications", "research"
        )

        return transferableSkillsFields.any { skill ->
            (field1.contains(skill) || field2.contains(skill))
        }
    }

    private fun calculateSkillsMatch(userSkills: List<String>, jobDescription: String,
                                   jobRequirements: String, userField: String): Int {
        if (userSkills.isEmpty()) return 20

        val jobText = "$jobDescription $jobRequirements".lowercase()
        var matchCount = 0
        var partialMatchCount = 0

        for (skill in userSkills) {
            val skillLower = skill.lowercase().trim()
            if (skillLower.isEmpty()) continue

            when {
                // Exact skill match
                jobText.contains(skillLower) -> matchCount++
                // Partial or related skill match
                hasRelatedSkill(skillLower, jobText, userField) -> partialMatchCount++
            }
        }

        val totalSkills = userSkills.size
        val exactMatchPercentage = (matchCount.toDouble() / totalSkills * 100).toInt()
        val partialMatchPercentage = (partialMatchCount.toDouble() / totalSkills * 50).toInt()

        val combinedMatch = exactMatchPercentage + partialMatchPercentage

        return when {
            combinedMatch >= 80 -> 90
            combinedMatch >= 60 -> 80
            combinedMatch >= 40 -> 70
            combinedMatch >= 20 -> 60
            combinedMatch > 0 -> 50
            else -> 30
        }.coerceAtMost(100)
    }

    private fun hasRelatedSkill(userSkill: String, jobText: String, userField: String): Boolean {
        // Define skill synonyms and related terms
        val skillSynonyms = mapOf(
            "javascript" to listOf("js", "node.js", "react", "angular", "vue"),
            "python" to listOf("django", "flask", "pandas", "numpy"),
            "java" to listOf("spring", "hibernate", "maven", "gradle"),
            "sql" to listOf("mysql", "postgresql", "database", "oracle"),
            "project management" to listOf("scrum", "agile", "kanban", "pmp"),
            "communication" to listOf("presentation", "public speaking", "writing"),
            "leadership" to listOf("management", "team lead", "supervision")
        )

        return skillSynonyms[userSkill]?.any { synonym ->
            jobText.contains(synonym)
        } ?: false
    }

    private fun calculateExperienceMatch(userYearsOfExperience: String, jobExperienceLevel: String): Int {
        if (userYearsOfExperience.isEmpty() || jobExperienceLevel.isEmpty()) return 70

        val userYears = extractYearsFromString(userYearsOfExperience)
        val requiredYears = extractYearsFromString(jobExperienceLevel)

        return when {
            userYears >= requiredYears -> 95
            userYears >= (requiredYears * 0.8) -> 85
            userYears >= (requiredYears * 0.6) -> 75
            userYears >= (requiredYears * 0.4) -> 60
            userYears > 0 -> 45
            else -> 30
        }
    }

    private fun extractYearsFromString(experienceString: String): Int {
        val lowerString = experienceString.lowercase()
        return when {
            lowerString.contains("no experience") || lowerString.contains("entry") -> 0
            lowerString.contains("less than 1") || lowerString.contains("0-1") -> 0
            lowerString.contains("1-2") -> 1
            lowerString.contains("2-3") -> 2
            lowerString.contains("3-5") -> 3
            lowerString.contains("5-7") -> 5
            lowerString.contains("7-10") -> 7
            lowerString.contains("10+") || lowerString.contains("senior") -> 10
            else -> {
                // Try to extract number from string
                val regex = Regex("(\\d+)")
                regex.find(lowerString)?.value?.toIntOrNull() ?: 2
            }
        }
    }

    private fun calculateOverallFit(userField: String, userSubField: String, jobDescription: String): Int {
        val description = jobDescription.lowercase()
        val field = userField.lowercase()
        val subField = userSubField.lowercase()

        return when {
            description.contains(field) && description.contains(subField) -> 90
            description.contains(field) -> 80
            description.contains(subField) -> 70
            else -> 60
        }
    }
}
